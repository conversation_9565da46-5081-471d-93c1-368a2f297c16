package com.payermax.channel.inst.center.facade.request.calendar;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class NumOfWorkdaysCalculationRequest extends HolidayCheckRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期(不为空)-包含
     */
    private String startDate;

    /**
     * 结束日期(不为空，且与startDate相差不超过365天)-包含
     */
    private String endDate;
}
