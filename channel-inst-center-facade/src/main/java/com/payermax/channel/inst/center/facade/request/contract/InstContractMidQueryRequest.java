package com.payermax.channel.inst.center.facade.request.contract;

import com.payermax.channel.inst.center.facade.enums.contract.ContractBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/20
 * @DESC
 */
@Data
public class InstContractMidQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道商户号
     */
    private String channelMerchantCode;

    /**
     * 业务类型
     * {@link ContractBizTypeEnum}
     */
    private String bizType;
}
