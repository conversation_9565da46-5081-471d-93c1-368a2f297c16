package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.DateUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2022-10-20 2:20 PM
 */
public class DoubleMonthCycleUnitHandler extends MonthCycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionTime) {
        String cycle = settleInfoVo.getSettleCycle();
        String limit = settleInfoVo.getSettleCycleOtherLimit();
        AssertUtil.isTrue(!StringUtil.isEmpty(cycle), "ERROR", "[SettleCycle] is mandatory");
        AssertUtil.isTrue(!StringUtil.isEmpty(limit), "ERROR", "[SettleCycleOtherLimit] is mandatory");
        //先要知道目前交易是在一年中的第几个月
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(transactionTime));
        int currentMonth = calendar.get(Calendar.MONTH);
        //推多少个月
        int months = Integer.parseInt(cycle);
        if (currentMonth % 2 == 0) {
            //双月是以1,2/3,4 为一个周期,再往后推进N个月,因此需要判断当前月落在第几个月,如果是单月需要再加一个月
            months += 1;
        }
        Date day = getDayOfNMonth(transactionTime, limit, months, settleInfoVo.isSkipHoliday());
        return DateUtil.addDays(day, settleInfoVo.getShift()).getTime();
    }
}
