package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.util.DateUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2022-10-20 2:18 PM
 */
public class DoubleWeekCycleUnitHandler implements CycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(transactionDay));
        int dom = calendar.get(Calendar.DAY_OF_MONTH);
        if (dom < 15) {
            calendar.set(Calendar.DAY_OF_MONTH, 15);
        } else {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DATE, -1);
        }
        return DateUtil.addDays(calendar.getTime(), settleInfoVo.getShift()).getTime();
    }
}
