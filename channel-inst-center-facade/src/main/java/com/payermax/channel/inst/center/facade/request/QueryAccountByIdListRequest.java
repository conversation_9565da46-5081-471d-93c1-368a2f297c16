package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import java.util.List;

/**
 * 根据账号标识和商户号查询账号信息request类
 *
 * <AUTHOR>
 * @date 2022/10/4 17:32
 */
@Data
public class QueryAccountByIdListRequest extends BaseRequest {

    private static final long serialVersionUID = -755025657869826L;

    /**
     * 机构账号标识*
     */
    private List<String> accountIds;

    /**
     * 商户号
     */
    private String merchantNo;

}
