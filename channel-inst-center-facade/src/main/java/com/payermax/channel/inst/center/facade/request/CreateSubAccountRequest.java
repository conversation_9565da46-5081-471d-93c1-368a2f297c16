package com.payermax.channel.inst.center.facade.request;

import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建子账号请求
 *
 * <AUTHOR>
 * @date 2022/10/2 20:13
 */
@Data
public class CreateSubAccountRequest extends BaseRequest {

    private static final long serialVersionUID = -2111218320910302316L;

    /**
     * 机构账号标识*
     *
     * 机构账号唯一键（如需要预筛选和创建接口是同一机构账号，此字段必传）
     */
    private String accountId;

    /**
     * 业务唯一key，会进行幂等校验
     */
    @NotBlank(message = "[businessKey] is mandatory")
    private String businessKey;

    /**
     * 商户号
     */
    @NotBlank(message = "[merchantNo] is mandatory")
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 国家
     */
    @NotBlank(message = "[country] is mandatory")
    private String country;

    /**
     * 币种
     */
    @NotBlank(message = "[currency] is mandatory")
    private String currency;

    /**
     * 子账号名称
     */
    private String subAccountName;

    /**
     * 用途
     */
    @NotNull(message = "[useType] is mandatory")
    private UseTypeEnum useType;

    /**
     * 申请子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
     */
    @NotNull(message = "[scenes] is mandatory")
    private String scenes;

    /**
     * 商户信息
     */
    private MerchantInfoRequest merchantInfo;

}
