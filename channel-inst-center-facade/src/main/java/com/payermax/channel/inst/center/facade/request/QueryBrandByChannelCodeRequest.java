package com.payermax.channel.inst.center.facade.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/8
 * @DESC 根据 channelCode 查询机构品牌
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryBrandByChannelCodeRequest extends BaseRequest implements Serializable {

    /**
     * 渠道编码列表
     */
    private List<String> channelCodeList;
}
