package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2023-08-12 10:57 AM
 */
@AllArgsConstructor
@Getter
public enum FeeCalculateBaseMode {

    /**
     * 交易金额 / (1 + 税率)
     */
    VAT_INCLUDED_IN_TPV("vat-include-in-tpv", "交易金额 / (1 + 税率)"),

    /**
     * 交易金额
     */
    FEE_ON_TPV("fee-on-tpv", "交易金额"),

    /**
     * 交易金额 + 税费
     */
    FEE_ON_TPV_AND_TAX("fee-on-tpv-and-tax", "交易金额 + 税费"),

    /**
     * 交易金额 - 税费
     */
    FEE_ON_TPV_EXCEPT_TAX("fee-on-tpv-except-tax", "交易金额 - 税费"),
    ;

    private final String code;

    private final String desc;


}
