package com.payermax.channel.inst.center.facade.response.calendar;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @DESC
 */
@Data
public class NextWorkdayCalculationResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    //------REQUEST-----//
    /**
     * 日期
     */
    private String date;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 日历类型，国家/币种/银行日历
     */
    private String calendarType;

    /**
     * 需要多少个工作日(0<N<365).
     */
    private Integer numOfWorkdays;


    //------RESULT-----//
    /**
     * 目标日期.满足从开始日期(包含)开始,数够足够工作日的下一天。
     * <p>
     * 例如开始日期是周一，目标是3个工作日。
     * <p>
     * 假设周一到周三没有节假日的情况下，targetDate会最终返回周四的日期。
     * <p>
     * 假设周一为节假日，会从下一个工作日(周二)开始计算，targetDate 最终会返回周五的日期。
     */
    private String targetDate;

}
