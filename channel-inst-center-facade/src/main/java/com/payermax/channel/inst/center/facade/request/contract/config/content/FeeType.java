package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2023-08-12 10:53 AM
 */
@AllArgsConstructor
@Getter
public enum FeeType {

    /**
     * PayIn  业务 包含 TRADE，REFUND，CHARGEBACK, SETTLEMENT
     * PayOut 业务 包含 TRADE
     */
    TRADE("TRADE FEE"),

    REFUND("REFUND FEE"),

    CHARGEBACK("CHARGEBACK FEE"),

    SETTLEMENT("SETTLEMENT FEE"),

    ;
    private final String desc;
}
