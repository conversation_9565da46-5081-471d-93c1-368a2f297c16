package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2023-08-12 10:59 AM
 */
@AllArgsConstructor
@Getter
public enum FeeCalculateTiming {

    /**
     * 交易日算费
     */
    CALCULATE_IN_TRADE_DAY("calculate-in-trade-day", "交易日算费"),

    /**
     * 结算日算费
     */
    CALCULATE_IN_SETTLEMENT_DAY("calculate-in-settlement-day", "结算日算费"),
    ;

    private final String code;

    private final String desc;

}
