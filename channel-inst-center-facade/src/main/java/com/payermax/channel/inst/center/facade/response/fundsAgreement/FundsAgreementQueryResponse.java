package com.payermax.channel.inst.center.facade.response.fundsAgreement;

import com.payermax.channel.inst.center.facade.enums.fundsAgreement.*;
import com.payermax.channel.inst.center.facade.response.BaseResponse;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FundsAgreementQueryResponse extends BaseResponse {


    private String name;

    private String bizAgreementNo;

    private String fundsAgreementNo;

    private String settleRuleNo;

    /**
     * 发起方
     */
    private String initiator;

    /**
     * 对手方
     */
    private String counter;

    /**
     * 业务协议类型
     */
    private InstBizAgreementType bizAgreementType;

    /**
     * 资金协议类型
     */
    private InstFundsAgreementType fundsAgreementType;


    /**
     * 清分币种
     */
    private String clearingCcy;

    /**
     * 清算模式: 实时 | 延时
     */
    private InstFundsAgreementClearingPattern clearingPattern;

    /**
     * 清分|清算规则
     */
    private FundsSettleRule fundsSettleRule;

    /**
     * 版本
     */
    private Integer version;


    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FundsSettleRule extends BaseResponse {

        /**
         * 时区-例:UTC8
         */
        private String timezone;

        /**
         * 切点-例:16:00:00
         */
        private String cutoffTime;

        /**
         * 清分模式-主动 | 被动
         */
        private InstFundsSettleRulePattern clearingPattern;

        /**
         * 清分范围开始
         */
        private String clearingRangeStart;

        /**
         * 清分范围结束
         */
        private String clearingRangeEnd;

        /**
         * 清分时间: D+1
         */
        private String clearingTime;

        /**
         * 用于计算的清分周期信息
         */
        private SettleInfoVo clearingPeriodInfoForCalculate;

        /**
         * 清偿币种
         */
        private String settleCcy;

        /**
         * 清偿模式-主动 | 被动
         */
        private InstFundsSettleRulePattern clearOffPattern;

        /**
         * 清偿类型-外部 | 内部
         */
        private InstFundsSettleRuleClearOffType clearOffType;

        /**
         * 清偿时间-出账单的规则
         */
        private String clearOffTime;

        /**
         * 清偿时间点, 例如: 01:00:00
         */
        private String clearOffTimePoint;

        /**
         * 用于计算的清偿时间信息
         */
        private SettleInfoVo clearOffPeriodInfoForCalculate;

        /**
         * 发起方付款账户
         */
        private String initiatorPaymentAccount;

        /**
         * 对手方付款账户
         */
        private String counterPaymentAccount;

        /**
         * 发起方收款账户
         */
        private String initiatorReceiveAccount;

        /**
         * 对手方收款账户
         */
        private String counterReceiveAccount;
    }

}
