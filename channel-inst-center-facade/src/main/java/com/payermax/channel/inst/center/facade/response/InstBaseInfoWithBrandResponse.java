package com.payermax.channel.inst.center.facade.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/5/8
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class InstBaseInfoWithBrandResponse extends BaseResponse{

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 机构品牌编码
     */
    private String brandCode;

    /**
     * 机构品牌名称
     */
    private String brandName;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 机构名称
     */
    private String instName;

    /**
     * 机构类型，可多个
     */
    private String instTypes;

    /**
     * 机构主体所在地
     */
    private String instEntityCountry;
}
