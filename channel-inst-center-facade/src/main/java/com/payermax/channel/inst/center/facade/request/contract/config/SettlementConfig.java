package com.payermax.channel.inst.center.facade.request.contract.config;

import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleAccount;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2023-08-12 11:26 AM
 */
@Getter
@Setter
public class SettlementConfig implements Serializable {

    /**
     * 合同结算条款项id
     */
    private String instContractSettlementItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 标准化后的内部渠道商户号
     */
    private String channelMerchantNo;

    /**
     * 我们自己内部的MCC,可以从交易链路中拿到
     */
    private String standardMcc;

    /**
     * 结算币种
     */
    private String settleCurrency;

    /**
     * 结算费用
     */
    private FeeConfig settleFeeConfig;

    /**
     * 结算周期-人能看懂
     */
    private List<SettleDate> settleDates;

    /**
     * 结算周期-计算使用
     */
    private SettleInfoVo settleInfoForCalculate;

    /**
     * 付款周期-计算使用
     */
    private SettleInfoVo fundsPaymentForCalculate;


    /**
     * 资金到账周期-计算使用
     */
    private SettleInfoVo fundsArrivedForCalculate;


    /**
     * 换汇周期-计算使用
     */
    private SettleInfoVo fundsExchangeForCalculate;

    /**
     * 提现打款
     */
    private SettleAccount settleAccount;

}
