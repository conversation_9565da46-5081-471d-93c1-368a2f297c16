package com.payermax.channel.inst.center.facade.response;

import com.payermax.channel.inst.center.facade.request.contract.InstContractBatchVerifyRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @DESC 结构合约批量校验
 */
@Data
@Accessors(chain = true)
public class InstContractBatchVerifyResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 校验数量
     */
    private Long verifyTotalCount;

    /**
     * 校验通过数量
     */
    private Long verifyPassCount;

    /**
     * 校验失败数量
     */
    private Long verifyFailCount;

    /**
     * 全部校验通过
     */
    private Boolean allVerifyPass;

    /**
     * 校验失败信息列表
     */
    private List<VerifyFailMsg> verifyFailMsgList;


    /**
     * 校验失败信息
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class VerifyFailMsg extends InstContractBatchVerifyRequest.VerifyItem implements Serializable{

        private static final long serialVersionUID = 1L;

        /**
         * 错误信息
         */
        private List<String> msg;

    }

}
