package com.payermax.channel.inst.center.facade.request.contract.config;

import com.payermax.channel.inst.center.facade.request.contract.config.content.TaxCalculateFormula;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> at 2023/6/12 3:02 PM
 * <p>
 * [
 * {
 * "taxType": "VAT",
 * "taxCalculateType": "",        // 税费计算公式，见4.1.1
 * "feeRateValue": 1.50,
 * "deductible": "DEDUCTIBLE"     // 是否可抵扣
 * }
 * ]
 **/
@Data
public class TaxConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 税种
     */
    private String taxType;

    /**
     * 计算方式
     * <p>
     * {@link TaxCalculateFormula}
     */
    private String taxCalculateType;

    /**
     * 费率百分比
     */
    private BigDecimal feeRateValue;

    /**
     * 该税种是否可抵扣
     * <p>
     * Y-可以抵扣
     * N-不可以抵扣
     */
    private String deductible;

}
