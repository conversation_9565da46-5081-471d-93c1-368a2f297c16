package com.payermax.channel.inst.center.facade.request.contract.config;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023-08-12 11:49 AM
 */
@Getter
@Setter
public class FxConfig implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 合约外汇加点
     */
    private BigDecimal contractFxSpread;

    /**
     * 外汇加点
     */
    private BigDecimal fxSpread;

    /**
     * 换汇时机
     * <p>
     * {@link  com.payermax.channel.inst.center.facade.request.contract.config.content.CurrencyExchangeTiming}
     */
    private String currencyExchangeTime;

}
