package com.payermax.channel.inst.center.facade.response;

import com.payermax.channel.inst.center.facade.enums.AccountExtKeyEnum;
import com.payermax.channel.inst.center.facade.enums.SupportSubAccountTypeEnum;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.FundsAccountExtVo;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 基础资金账户响应类
 *
 * <AUTHOR>
 * @date 2022/10/2 20:27
 */
@Data
public class BaseFundsAccountResponse extends BaseResponse {

    private static final long serialVersionUID = 6318024653322062362L;

    /**
     * 机构账号标识*
     */
    private String accountId;

    /**
     * 机构标识*
     */
    private String instCode;

    /**
     * 签约主体*
     */
    private String entity;

    /**
     * 国家*
     */
    private String country;

    /**
     * 币种*
     */
    private String currency;

    /**
     * 账号*
     */
    private String accountNo;

    /**
     * 账号名称*
     */
    private String accountName;

    /**
     * 账号类型*
     */
    private String accountType;

    /**
     * 场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款、
     * 支持多个以,分割
     */
    private String scenes;

    /**
     * 是否支持子级账号 N:否 Y:是*
     */
    private String isSupportSubAccount;

    /**
     * 支持子级账号类型 SupportSubAccountTypeEnum，英文,分割
     */
    private String supportSubAccountType;

    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;

    /**
     * 是否支持自定义子账号名称 N:否 Y:是*
     */
    private String isSupportCustomName;

    /**
     * 银行名称*
     */
    private String bankName;

    /**
     * 银行地址*
     */
    private String bankAddress;

    /**
     * 银行地址 JSON 结构
     * {
     * "city": "city",
     * "address": "address",
     * "country": "country",
     * "postcode": "postcode"
     * }
     */
    private String bankAddressJson;

    /**
     * iban
     */
    private String iban;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 账户关联的渠道MID.
     */
    private List<String> relatedMidList;

    /**
     * 子级账号名称规则
     */
    private String subAccountNameWebRule;

    /**
     * 扩展属性*
     */
    private List<FundsAccountExtVo> accountExtList;

    /**
     * 获取扩展属性值*
     *
     * @param extKeyEnum
     * @return
     */
    public String getAccountExtValue(AccountExtKeyEnum extKeyEnum) {
        if (accountExtList == null || accountExtList.isEmpty() || extKeyEnum == null) {
            return null;
        }
        FundsAccountExtVo accountExtVo = accountExtList.stream().filter(t -> extKeyEnum.getCode().equals(t.getExtKey()))
                .findFirst().orElse(null);// NO_CHECK
        if (accountExtVo == null) {
            return null;
        }
        return accountExtVo.getExtValue();
    }

    /**
     * 检查是否支持VA充值*
     *
     * @return
     */
    public boolean isSupportVaRecharge() {
        if (StringUtil.isEmpty(this.isSupportSubAccount) || (!Objects.equals(this.isSupportSubAccount, "Y")) || StringUtil.isEmpty(this.supportSubAccountType)) {
            return false;
        }
        String[] supportSubAccountTypes = this.supportSubAccountType.split(",");
        return Arrays.asList(supportSubAccountTypes).contains(SupportSubAccountTypeEnum.VA_RECHARGE.name());
    }

    /**
     * 检查是否支持VA收款*
     *
     * @return
     */
    public boolean isSupportVaReceivePay() {
        if (StringUtil.isEmpty(this.isSupportSubAccount) || (!Objects.equals(this.isSupportSubAccount, "Y")) || StringUtil.isEmpty(this.supportSubAccountType)) {
            return false;
        }
        String[] supportSubAccountTypes = this.supportSubAccountType.split(",");
        return Arrays.asList(supportSubAccountTypes).contains(SupportSubAccountTypeEnum.VA_RECEIVE_PAY.name());
    }
}
