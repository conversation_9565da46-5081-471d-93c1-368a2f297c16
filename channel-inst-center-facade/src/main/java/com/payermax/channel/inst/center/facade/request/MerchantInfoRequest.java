package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2024/03/06 16:40
 */
@Data
public class MerchantInfoRequest implements Serializable {

    private static final long serialVersionUID = -288320910302316L;

    /**
     * 商户CI号
     */
    private String merchantCi;

    /**
     * 商户注册地址信息
     */
    private String merchantAddress;

    /**
     * 商户主体所在地
     */
    private String merchantEntityCountry;

    /**
     * 商户主要办公地点
     */
    private String merchantOfficeLocation;

    /**
     * 商户注册日期
     */
    private String merchantRegisteredDate;

    /**
     * 商户注册名（中文名）
     */
    private String merchantRegisteredChineseName;

    /**
     * 法人名称
     * */
    private Name legalPersonName;
    
    /**
     * 受益人名称
     * */
    private List<Name> uboName;
    
    @Data
    public static class Name implements Serializable {

        private static final long serialVersionUID = -288320910302317L;
        
        private String firstName;

        private String lastName;
    }
}
