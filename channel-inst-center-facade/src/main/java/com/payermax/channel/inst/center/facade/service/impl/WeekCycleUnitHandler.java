package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.DateUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.*;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 2:18 PM
 */
public class WeekCycleUnitHandler implements CycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionDay) {
        String cycle = settleInfoVo.getSettleCycle();
        String limit = settleInfoVo.getSettleCycleOtherLimit();

        // 默认周期处理----开始是1～7
        int start = settleInfoVo.getCycleStart();
        if (start == 0) {
            start = 1;
        }
        AssertUtil.isTrue(start >= 1 && start <= 7, "ERROR", "CycleStart is invalid");
        int end = settleInfoVo.getCycleEnd();
        if (end == 0) {
            end = 7;
        }
        AssertUtil.isTrue(end >= 1 && end <= 7, "ERROR", "CycleEnd is invalid");
        AssertUtil.isTrue(!StringUtil.isEmpty(cycle), "ERROR", "[SettleCycle] is mandatory");
        AssertUtil.isTrue(!StringUtil.isEmpty(limit), "ERROR", "[SettleCycleOtherLimit] is mandatory");

        // 周几
        int weekDay = Integer.parseInt(limit);
        AssertUtil.isTrue(weekDay >= 1 && weekDay <= 7, "ERROR", "SettleCycleOtherLimit is invalid");

        // 几周后
        int weeks = Integer.parseInt(cycle);

        // 通过周的区间看下最终是在几周后
        int cycleNeedShit = calculateCycleWeekShift(transactionDay, start, end, weekDay);
        weeks = weeks + cycleNeedShit;

        // java日历的day周日是1,周一是2……
        Date day = getDayOfNWeek(weekDay, weeks, transactionDay);

        // 最后再处理shift
        return DateUtil.addDays(day, settleInfoVo.getShift()).getTime();
    }

    /**
     * 由于周也有周期,例如某个渠道 约定周三到下周二是一个周期，统一在下周的周四结算。
     * <p>
     * 那么当当前时间落在周三时，结算时间是在下周四；
     * 但是当当前时间落在周二时，结算时间则为本周四；
     * <p>
     * 所以这里本质是在区分出当前的时间到底是在渠道的哪个周期里，判断依据为是否大于了开始的周几数
     */
    private int calculateCycleWeekShift(long transactionDay, int start, int end, int weekDay) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionDay));
        // java日历的day周日是1,周一是2……
        int nowDayOfWeek = date.get(Calendar.DAY_OF_WEEK) - 1;
        if (nowDayOfWeek == 0) {
            nowDayOfWeek = 7;
        }
        if(start == 1){
            return 0;
        }

        // 判断交易日在周期的上/下半部分
        boolean inUpRange = start <= nowDayOfWeek && nowDayOfWeek <= 7;
        boolean inDownRange = 1 <= nowDayOfWeek && nowDayOfWeek <= end;

        // 如果交易日在周期的上半部分，并且结算日在周期的下半部分，需要 +1 推到交易日所在的下周
        if (inUpRange && weekDay <= end) {
            return +1;
        }
        // 如果交易日在周期的下半部分，并且结算日在周期的上半部分，需要 -1 推到交易日所在的上周
        else if(inDownRange && weekDay > end){
            return -1;
        }
        // 其他情况则结算日和交易日在同一部分，不需要处理
        else{
            return 0;
        }
    }


    public static Date getDayOfNWeek(int weekDay, int weeks, long transactionDay) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionDay));
        // 获取当前日期与目标日期的差异
        int nowDayOfWeek  = date.get(Calendar.DAY_OF_WEEK) - 1;
        if(nowDayOfWeek == 0){
            nowDayOfWeek = 7;
        }
        int diff = weekDay - nowDayOfWeek;
        // 先跨一周找到下周
        diff += weeks * 7;
        // 把这个差异加上,就可以得到目标日期
        date.add(Calendar.DAY_OF_MONTH, diff);
        return date.getTime();
    }
}
