package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> at 2023/6/14 8:05 PM
 **/
@AllArgsConstructor
@Getter
public enum CurrencyExchangeTiming {

    /**
     * 交易日换汇
     */
    EXCHANGE_IN_TRADE_DAY("exchange-in-trade-day", "交易日换汇"),

    /**
     * 结算日换汇
     */
    EXCHANGE_IN_SETTLEMENT_DAY("exchange-in-settlement-day", "结算日换汇"),

    /**
     * 提现日换汇
     */
    EXCHANGE_IN_WITHDRAW_DAY("exchange-in-withdraw-day", "提现日换汇"),

    /**
     * 不换汇
     */
    NO_EXCHANGE("on-exchange", "不换汇"),
    ;

    private final String code;

    private final String desc;

}
