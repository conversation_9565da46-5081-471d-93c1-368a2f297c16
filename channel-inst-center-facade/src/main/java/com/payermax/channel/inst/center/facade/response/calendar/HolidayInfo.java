package com.payermax.channel.inst.center.facade.response.calendar;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> t<PERSON>
 * @version 2024/12/9 11:31
 */
@Getter
@Setter
@ToString
public class HolidayInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否是节假日
     */
    private Boolean isHoliday;

    /**
     * 日历类型
     */
    private String calendarType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 节假日名称
     */
    private String holidayName;
}
