package com.payermax.channel.inst.center.facade.response;

import com.payermax.channel.inst.center.facade.request.BaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * QueryInstBrandResponse
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class QueryInstBrandResponse extends BaseRequest {

    private static final long serialVersionUID = -9131172404802198033L;

    private Long brandId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * bd shareId
     */
    private String bdId;

    /**
     * bd姓名
     */
    private String bdName;

    /**
     * 状态，Y：可用、N：不可用
     */
    private String status;
}
