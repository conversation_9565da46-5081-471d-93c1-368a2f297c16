package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2023-08-12 11:00 AM
 */
@AllArgsConstructor
@Getter
public enum FeeCalculateType {

    /**
     * 单笔按笔
     */
    SINGLE_RATE("single-rate", "单笔比例"),

    /**
     * 单笔固定
     */
    SINGLE_MONEY("single-money", "单笔固定"),

    /**
     * 单笔组合
     */
    SINGLE_COMBINE("single-combine", "单笔组合"),

    /**
     * 阶梯计费
     */
    STEP_COMBINE("step-combine", "阶梯计费"),
    ;


    private final String code;

    private final String desc;

}
