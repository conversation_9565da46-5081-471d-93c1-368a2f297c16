package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询子账号详情请求类
 *
 * <AUTHOR>
 * @date 2022/10/2 20:19
 */
@Data
public class QueryAccountDetailInfoRequest extends BaseRequest {

    private static final long serialVersionUID = 707358160965571871L;

    /**
     * 国家
     */
    @NotBlank(message = "[country] is mandatory")
    private String country;

    /**
     * 币种
     */
    @NotBlank(message = "[currency] is mandatory")
    private String currency;

    /**
     * 机构标识
     */
    @NotBlank(message = "[instCode] is mandatory")
    private String instCode;

    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构子账号
     */
    private String subAccountNo;

    /**
     * 机构子账号BBAN
     */
    private String bSubAccountNo;

    /**
     * 渠道VA唯一编号
     */
    private String channelUniqueNumber;

}
