package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> at 2025/6/25 17:49
 **/
@Data
public class QueryAccountByChannelMerchantCodesRequest implements Serializable {

    private static final long serialVersionUID = -4719790086844475193L;

    @NotNull(message = "channelMerchantCodeList is mandatory")
    @Size(min = 1, message = "channelMerchantCodeList is empty")
    private List<String> channelMerchantCodeList;

    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     **/
    @NotNull(message = "useType is mandatory")
    @NotEmpty(message = "useType is empty")
    @NotBlank(message = "useType is blank")
    private String useType;
}
