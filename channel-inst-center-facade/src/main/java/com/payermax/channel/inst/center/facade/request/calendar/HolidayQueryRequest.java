package com.payermax.channel.inst.center.facade.request.calendar;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> tracy
 * @version 2024/12/9 11:16
 */
@Getter
@Setter
@ToString
public class HolidayQueryRequest implements Serializable {

    private static final long serialVersionUID = 4184851533750629413L;
    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 日历类型，国家/币种/银行日历
     */
    private String calendarType;

    /**
     * 开始日期(不为空)-包含
     */
    private String startDate;

    /**
     * 结束日期(不为空，且与startDate相差不超过365天)-包含
     */
    private String endDate;

    /**
     * 是否包含周六周日。
     * <p>
     * TRUE-结果中需要包含周六周日
     * FALSE-结果中不需要包含周六周日
     */
    private Boolean containsWeekends;
}
