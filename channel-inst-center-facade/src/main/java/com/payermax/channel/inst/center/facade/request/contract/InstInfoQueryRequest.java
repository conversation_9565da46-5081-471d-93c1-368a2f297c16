package com.payermax.channel.inst.center.facade.request.contract;

import com.payermax.channel.inst.center.facade.enums.contract.CardTypeEnum;
import com.payermax.channel.inst.center.facade.enums.contract.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.enums.financialCalendar.CalendarTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR> tracy
 * @version 2023-08-11 5:31 PM
 */
@Getter
@Setter
@EqualsAndHashCode
public class InstInfoQueryRequest implements Serializable {

    /**
     * 业务大类 - 入款(I) OR 出款(O) OR 风控(R) OR 技术服务费(TS) OR VA(VA)
     * <p>
     * {@link ContractBizTypeEnum}
     */
    @NotBlank
    private String bizType;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 渠道商户号
     */
    @NotBlank
    private String channelMerchantCode;

    /**
     * 支付方式类型
     */
    @NotBlank
    private String paymentMethodType;

    /**
     * 支付币种
     */
    @NotBlank
    private String payCurrency;

    /**
     * 交易国家
     */
    private String transactionCountry;

    /**
     * 清算网络
     */
    private String clearingNetwork;

    /**
     * 费用承担方
     */
    private String feeBearer;

    /**
     * 客户类型
     * {@link CalendarTypeEnum}
     */
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    private String cardType;

    /**
     * 目标机构-和cardOrg二选一
     */
    private String targetOrg;

    /**
     * 卡组织-和targetOrg二选一
     */
    private String cardOrg;

    /**
     * mcc
     */
    private String mcc;

    /**
     * 渠道子商户号
     */
    private String subMerchantNo;

    /**
     * 资金源
     */
    private String fundingSource;

    /**
     * 交易时间
     */
    private long transactionTime;

}
