package com.payermax.channel.inst.center.facade.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-19 8:37 PM
 */
@Getter
@Setter
public class FxInfoQueryRequest extends BaseRequest {

    /**
     * 渠道编码
     */
    @NotBlank(message = "[chanelCode] is mandatory")
    private String chanelCode;

    /**
     *渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 支付方式编码
     */
    private String channelMethodCode;

    /**
     * 签约主体
     */
    @NotBlank(message = "[entity] is mandatory")
    private String entity;

    /**
     * 支付方式类型
     */
    @NotBlank(message = "[paymentMethodType] is mandatory")
    private String paymentMethodType;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 卡组织
     */
    private String cardOrg;

    /**
     * 支付币种
     */
    @NotBlank(message = "[paymentCcy] is mandatory")
    private String paymentCcy;
}
