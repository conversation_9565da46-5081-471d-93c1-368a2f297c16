package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2023/8/10  3:06 PM
 */
@Data
public class InstBankAccountRequest extends BaseRequest {

    private static final long serialVersionUID = -5560491385048896577L;

    /**
     * 机构标识
     */
    @NotBlank(message = "instCode is mandatory")
    private String instCode;

    /**
     * 账户用途，见 UseTypeEnum
     */
    private String useType;

}