package com.payermax.channel.inst.center.facade.request.contract.config;

import com.payermax.channel.inst.center.facade.request.contract.config.content.FeeCalculateBaseMode;
import com.payermax.channel.inst.center.facade.request.contract.config.content.FeeCalculateTiming;
import com.payermax.channel.inst.center.facade.request.contract.config.content.FeeCalculateType;
import com.payermax.channel.inst.center.facade.request.contract.config.content.FeeType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-12 10:52 AM
 */
@Getter
@Setter
public class FeeConfig implements Serializable {

    /**
     * 费用类型
     * <p>
     * {@link  FeeType}
     */
    private String feeType;

    /**
     * 交易费用算费基准，针对交易金额，还是 交易金额+税费金额一起收 手续费
     * <p>
     * {@link FeeCalculateBaseMode}
     */
    private String feeBasementMode;

    /**
     * 算费时机，交易日算费 还是 结算日算费
     * 影响「渠道账」清分步骤 银行时间，进而影响月结/日结进程
     * <p>
     * {@link FeeCalculateTiming}
     */
    private String feeCalculateTiming;

    /**
     * 扣费币种
     */
    private String feeDeductCurrency;

    /**
     * 算费方式
     * <p>
     * {@link  FeeCalculateType}
     */
    private String calculateType;

    /**
     * 费率百分比
     */
    private BigDecimal feeRateValue;

    /**
     * 百分比计算上下限及币种（百分比计算时封顶、最低费用）
     */
    private BigDecimal percentMinAmount;

    private BigDecimal percentMaxAmount;

    private String percentMinAmountCurrency;

    private String percentMaxAmountCurrency;

    /**
     * 固定手续费金额 + 币种
     */
    private BigDecimal feeValue;

    /**
     * 费用币种
     */
    private String feeCurrency;

    /**
     * 阶梯费用的周期.
     * <p>
     * MONTH - 月
     * YEAR  - 年
     */
    private String accumulationCycle;

    /**
     * 阶梯统计模式
     * <p>
     * NUMBER - 按笔数
     * AMOUNT - 按金额
     */
    private String accumulationType;

    /**
     * 阶梯统计模式
     * <p>
     * NUMBER - 按笔数
     * AMOUNT - 按金额
     */
    private String accumulationMethod;

    /**
     * 阶梯统计的范围
     * <p>
     * PARTS -- 部分
     * TOTAL -- 全部
     */
    private String accumulationRange;

    /**
     * 阶梯扣费时机
     * <p>
     * PERIODIC -- 定期
     * REALTIME -- 实时
     */
    private String accumulationDeductTime;

    /**
     *
     */
    private String accumulationJoin;

    private String accumulationKey;

    private String accumulationMode;
    /**
     * 是否大阶梯
     */
    private boolean isAccumulation;

    /**
     * 阶梯费率配置- 仅当 calculateType == 'STEP_COMBINE' 时需要用到。
     */
    private List<FeeStepCombineConfig> stepPercentAmount = new ArrayList<>();

    /**
     * 例如 退款手续费配置，包含FEE_REFUND字段，表示正向交易费用是否退回
     */
    private Map<String, String> extendFields = new HashMap<>();

    /**
     * 阶梯计费，每个阶梯定义
     * <p>
     * 阶梯即在特定的交易量区间内使用不同的费率模式。
     * <p>
     * 例如 0  ～100万 费率-> 3%
     * 例如 100～500万 费率-> 2%
     * 例如 500～ +∞   费率-> 1%
     * <p>
     * 阶梯详细列表内,包含了左闭右开的两个金额，以及在这个金额区间内的计费方式。
     * <p>
     * 区间内的计费方式依然会包含 单笔比例、单笔固定、单笔组合 三种模式
     */
    @Getter
    @Setter
    public static class FeeStepCombineConfig implements Comparable<FeeStepCombineConfig>, Serializable {

        /**
         * 左闭右开
         */
        private BigDecimal leftAmount;

        private BigDecimal rightAmount;

        /**
         * 算费方式
         */
        private String calculateType;

        /**
         * 费率百分比
         */
        private BigDecimal feeRateValue;

        /**
         * 百分比计算上下限及币种
         */
        private BigDecimal percentMinAmount;

        private BigDecimal percentMaxAmount;

        private String percentMinAmountCurrency;

        private String percentMaxAmountCurrency;

        /**
         * 固定手续费金额 + 币种
         */
        private BigDecimal feeValue;

        private String feeCurrency;

        @Override
        public int compareTo(FeeStepCombineConfig o) {
            if (leftAmount == null || o.getLeftAmount() == null) {
                return 0;
            }

            // 左开右闭，按照阶梯金额从小到大排序
            return leftAmount.compareTo(o.getLeftAmount());
        }
    }
}
