package com.payermax.channel.inst.center.facade.request.contract.config.mapping;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 机构中心机构+渠道MID映射请求
 *
 * <AUTHOR>
 * @version 2023-08-11 5:23 PM
 */
@Getter
@Setter
public class InstChannelMerchantCodeRequest implements Serializable {

    /**
     * 机构类型: I/O
     */
    @NotBlank
    private String instType;

    /**
     * 机构编码
     */
    @NotBlank
    private String instCode;

    /**
     * 渠道签约主体
     */
    @NotBlank
    private String entity;

    /**
     * 渠道商户编码
     */
    @NotBlank
    private String channelMerchantCode;


}
