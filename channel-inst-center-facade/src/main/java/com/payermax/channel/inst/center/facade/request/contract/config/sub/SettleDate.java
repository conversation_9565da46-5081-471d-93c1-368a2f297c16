package com.payermax.channel.inst.center.facade.request.contract.config.sub;

import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> at 2023/6/12 8:33 PM
 **/
@Data
public class SettleDate implements Serializable {

    private static final long serialVersionUID = 1L;

    private String transactionStartDate;

    private String transactionEndDate;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 账单日
     */
    private String billDate;

    /**
     * 打款日
     */
    private String paymentDate;

    /**
     * 到账日
     */
    private String arriveDate;

    /**
     * 换汇日
     */
    private String exchangeDate;

    /**
     * 机器识别的dsl
     */
    private SettleRoundDSLEntity dslEntity;
}
