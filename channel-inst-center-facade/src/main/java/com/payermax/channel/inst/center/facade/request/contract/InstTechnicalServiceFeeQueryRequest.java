package com.payermax.channel.inst.center.facade.request.contract;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/1/2
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstTechnicalServiceFeeQueryRequest extends InstInfoQueryRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 支付业务类型: I/O/VA
     */
    @NotBlank(message = "支付业务类型")
    private String paymentBusinessType;

}
