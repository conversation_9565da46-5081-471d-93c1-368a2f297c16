package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.DateUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Date;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 2:18 PM
 */
public class DayCycleUnitHandler implements CycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionTime) {
        //找到单位的值
        String cycle = settleInfoVo.getSettleCycle();
        AssertUtil.isTrue(!StringUtil.isEmpty(cycle), "ERROR", "[SettleCycle] is mandatory");
        int day = Integer.parseInt(cycle);
        // 判断是否跳过节假日
        if(settleInfoVo.isSkipHoliday()){
            return SettleDateUtils.addDaysSkipWeekends(transactionTime, day,false).getTime().getTime();
        }
        return DateUtil.addDays(new Date(transactionTime), day).getTime();
    }

}
