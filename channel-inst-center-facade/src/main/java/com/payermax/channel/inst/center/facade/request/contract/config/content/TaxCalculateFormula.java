package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> at 2023/6/12 3:04 PM
 **/
@AllArgsConstructor
@Getter
public enum TaxCalculateFormula {

    VAT_INCLUDED_IN_TPV("vat-included-in-tpv", "交易金额 * 税率 / (1 + 税率)"),

    TAX_ON_TPV("tax-on-tpv", "交易金额 * 税率"),

    TAX_ON_FEE("tax-on-fee", "手续费金额 * 税率"),

    TAX_ON_TPV_EXCLUDE_FEE("tax-on-tpv-exclude-fee", "(交易金额 - 手续费) * 税率"),
    ;

    private final String code;

    private final String desc;

}
