package com.payermax.channel.inst.center.facade.response.contract;

import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstTechnicalServiceFeeResponse extends InstInfoQueryResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 支付业务类型: I/O/VA
     */
    private String paymentBusinessType;

}

