package com.payermax.channel.inst.center.facade.response;

import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> tracy
 * @version 2022-10-19 8:49 PM
 */
@Getter
@Setter
public class FxInfoQueryResponse extends BaseResponse {
    
    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 卡组织
     */
    private String cardOrg;

    /**
     * 支付币种
     */
    private String paymentCcy;

    /**
     * 换汇类型(交易换汇/结算换汇/提现换汇/其他)
     * SettlementDay/TransactionDay/WithdrawDay
     */
    private String exchangeType;

    /**
     * 结算信息
     */
    private SettleInfoVo settleInfoVo;
}
