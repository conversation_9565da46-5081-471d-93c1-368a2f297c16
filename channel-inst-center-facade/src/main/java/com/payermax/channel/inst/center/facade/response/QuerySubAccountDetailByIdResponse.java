package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

import java.util.Date;

/**
 * 根据账号标识查询账号信息response类
 *
 * <AUTHOR>
 * @date 2022/10/4 17:33
 */
@Data
public class QuerySubAccountDetailByIdResponse extends BaseFundsAccountResponse {

    private static final long serialVersionUID = -8677955705117858872L;

    /**
     * 子级账号标识
     */
    private String subAccountId;

    /**
     * 业务申请唯一键
     */
    private String businessKey;

    /**
     * 号段生成的账号号码
     */
    private String numberSegmentNo;

    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;

    /**
     * 子级账号号码
     */
    private String subAccountNo;

    /**
     * 子级账号名称
     */
    private String subAccountName;

    /**
     * 子级账号号码BBAN
     */
    private String bSubAccountNo;

    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;

    /**
     * 申请子级账号的子商户号
     */
    private String subMerchantNo;

    /**
     * 状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer subAccountStatus;

    /**
     * 申请子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     */
    private String subScenes;

    /**
     * 账号相关拓展信息，json格式
     */
    private String accountJson;

    /**
     * 创建时间
     */
    private Date subUtcCreate;
}
