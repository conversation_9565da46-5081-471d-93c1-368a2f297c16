package com.payermax.channel.inst.center.facade.response.calendar;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class NumOfWorkdaysCalculationResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    //------REQUEST-----//
    /**
     * 日期
     */
    private String date;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 日历类型，国家/币种/银行日历
     */
    private String calendarType;

    /**
     * 开始日期(不为空)
     */
    private String startDate;

    /**
     * 结束日期(不为空，且与startDate相差不超过365天)
     */
    private String endDate;

    //------RESULT-----//
    /**
     * 有多少个工作日
     */
    private Integer numOfWorkdays;

}
