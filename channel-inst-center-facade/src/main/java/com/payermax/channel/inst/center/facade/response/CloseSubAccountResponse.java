package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

/**
 * 创建子账号响应类
 *
 * <AUTHOR>
 * @date 2022/10/2 20:29
 */
@Data
public class CloseSubAccountResponse extends BaseFundsAccountResponse {

    private static final long serialVersionUID = -68722167359727L;

    /**
     * 机构子账号标识*
     */
    private String subAccountId;

    /**
     * 机构子账号号码*
     */
    private String subAccountNo;

    /**
     * 机构子账号名称*
     */
    private String subAccountName;

    /**
     * 机构子账号号码BBAN*
     */
    private String bSubAccountNo;

    /**
     * 机构子账号状态：初始态、申请中、待激活、激活中、已激活、已停用*
     */
    private Integer subAccountStatus;

    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;

    /**
     * 申请子级账号的子商户号
     */
    private String subMerchantNo;

    /**
     * 子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     * 支持多个以,分割
     */
    private String subScenes;

}
