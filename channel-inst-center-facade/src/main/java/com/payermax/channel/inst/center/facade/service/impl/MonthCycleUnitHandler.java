package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.DateUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-20 2:18 PM
 */
public class MonthCycleUnitHandler implements CycleUnitHandler {

    private static final String LAST_DAY = "LAST_DAY";

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionTime) {
        String cycle = settleInfoVo.getSettleCycle();
        String limit = settleInfoVo.getSettleCycleOtherLimit();
        AssertUtil.isTrue(!StringUtil.isEmpty(cycle), "ERROR", "[SettleCycle] is mandatory");
        AssertUtil.isTrue(!StringUtil.isEmpty(limit), "ERROR", "[SettleCycleOtherLimit] is mandatory");

        //处理月度周期
        int start = settleInfoVo.getCycleStart();
        int end = settleInfoVo.getCycleEnd();
        if (start == 0) {
            start = 1;
        }
        if (end == 0) {
            end = -1;
        }
        AssertUtil.isTrue(start >= 1 && start <= 31, "ERROR", "start is invalid");
        AssertUtil.isTrue(end <= 31, "ERROR", "end is invalid");

        //推多少个月
        int months = Integer.parseInt(cycle);

        //计算因为周期导致的偏移,周期偏移为0/1/-1,代表偏移的相对周期数而不是绝对值
        int monthShift = calculateCycleMonthShift(transactionTime, start, end);

        // 得到最终推进的月数,相对周期*单个周期值=据对周期数
        months = months + monthShift * months;

        Date day = getDayOfNMonth(transactionTime, limit, months, settleInfoVo.isSkipHoliday());
        return DateUtil.addDays(day, settleInfoVo.getShift()).getTime();
    }

    protected Date getDayOfNMonth(long transactionTime, String limit, int months, boolean isSkipHoliday) {
        Calendar cal = Calendar.getInstance();
        int day;
        if (LAST_DAY.equals(limit)) {
            // 兼容历史上的 字符串传递场景
            day = -1;
        } else {
            day = Integer.parseInt(limit);
        }
        cal.setTime(new Date(transactionTime));
        if (day > 0) {
            //强制指定日期,再推进N个月
            cal.add(Calendar.MONTH, months);
            cal.set(Calendar.DAY_OF_MONTH, day);
            if(isSkipHoliday){
                // 跳过周末
                cal.setTime(SettleDateUtils.skipWhenWeekends(cal.getTime()));
            }

        } else {
            // 如果是负数,那么本质上是从下下个月往回推
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.add(Calendar.MONTH, months + 1);
            cal.add(Calendar.DATE, day);
            if(isSkipHoliday){
                // 跳过周末
                cal.setTime(SettleDateUtils.pushbackWhenWeekends(cal.getTime()));
            }
        }
        return cal.getTime();
    }

    private int calculateCycleMonthShift(long transactionDay, int start, int end) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionDay));
        int nowDayOfMonth = date.get(Calendar.DAY_OF_MONTH);

        // end 用当月数据正数化处理
        if (end < 0) {
            int lastDayOfMonth = calculateTimesMonthLastDay(transactionDay);
            end = lastDayOfMonth + end + 1;
        }

        if (nowDayOfMonth >= start && nowDayOfMonth <= end) {
            //落在正常的周期区间内
            return 0;
        } else {
            throw new UnsupportedOperationException("月度数据有问题" + start + " " + end);
        }
    }

    private static int calculateTimesMonthLastDay(long transactionDay) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(transactionDay));
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.MONTH, 1);
        cal.add(Calendar.DATE, -1);
        return cal.get(Calendar.DAY_OF_MONTH);
    }


}

