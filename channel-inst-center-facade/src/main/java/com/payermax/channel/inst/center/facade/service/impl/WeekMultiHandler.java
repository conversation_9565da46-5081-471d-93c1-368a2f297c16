package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> t<PERSON>
 * @version 2024/1/25 15:05
 */
public class WeekMultiHandler extends WeekCycleUnitHandler implements CycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionTime) {
        String otherLimit = settleInfoVo.getSettleCycleOtherLimit();
        String[] days = otherLimit.split(",");
        // 现在是在周几
        int dayOfWeek = getDayOfWeek(transactionTime);
        // 最终要在周几,JAVA 要加1
        int weekDay = getFinalWeekDay(dayOfWeek, days);
        if (weekDay >= dayOfWeek) {
            return getDayOfNWeek(weekDay - dayOfWeek, transactionTime).getTime();
        } else {
            return getDayOfNWeek(weekDay - dayOfWeek + 7, transactionTime).getTime();
        }
    }

    private int getFinalWeekDay(int dayOfWeek, String[] days) {
        int rangeStart = Integer.parseInt(days[days.length - 1]);
        for (String kk : days) {
            int round = Integer.parseInt(kk);
            if (dayOfWeek >= rangeStart && dayOfWeek < round) {
                return round;
            } else {
                rangeStart = round;
            }
        }
        //代表是最后一个到第一个的区间
        return Integer.parseInt(days[0]);
    }

    private int getDayOfWeek(long transactionTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionTime));
        //java日历的day周日是1,周一是2……
        int nowDayOfWeek = date.get(Calendar.DAY_OF_WEEK) - 1;
        if (nowDayOfWeek == 0) {
            nowDayOfWeek = 7;
        }
        return nowDayOfWeek;
    }

    public Date getDayOfNWeek(int days, long transactionDay) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionDay));
        //把这个差异加上,就可以得到目标日期
        date.add(Calendar.DAY_OF_MONTH, days);
        return date.getTime();
    }
}
