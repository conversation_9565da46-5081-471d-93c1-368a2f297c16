package com.payermax.channel.inst.center.facade.request.contract;

import com.payermax.channel.inst.center.facade.enums.contract.CardTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2023-08-11 5:31 PM
 */
@Getter
@Setter
public class InstInfoQueryResponse implements Serializable {


    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 合同版本
     */
    private String contractVersion;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 业务大类 - 入款(I) OR 出款(O)
     */
    private String bizType;

    /**
     * 费用信息编号
     */
    private String instContractFeeItemNo;

    /**
     * 渠道商户号
     */
    private String channelMerchantCode;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 目标机构-和cardOrg二选一
     */
    private String targetOrg;

    /**
     * 卡组织-和targetOrg二选一
     */
    private String cardOrg;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * mcc
     */
    private String mcc;

    /**
     * 清算网络
     */
    private String clearingNetwork;

    /**
     * 费用承担方
     */
    private String feeBearer;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    private String cardType;

    /**
     * 渠道子商户号
     */
    private String subMid;

    /**
     * 交易国家
     */
    private String transactionCountry;

    /**
     * 交易时间
     */
    private long transactionTime;

    /**
     * 合同交易费用信息
     * <p>
     * key {@link com.payermax.channel.inst.center.facade.request.contract.config.content.FeeType}
     */
    private Map<String, FeeConfig> feeConfigMap;

    /**
     * 计算精度
     */
    private Integer roundingScale;

    /**
     * 小数位调整模式
     */
    private String roundingMode;

    /**
     * 税费信息
     */
    private List<TaxConfig> taxConfig;

    /**
     * 合同结算条款-->包含结算费用
     */
    private SettlementConfig settlementConfig;

    /**
     * fx信息
     */
    private FxConfig fxConfig;

    /**
     * 扣款信息(出款才有)
     */
    private DeductConfig deductConfig = new DeductConfig();
}
