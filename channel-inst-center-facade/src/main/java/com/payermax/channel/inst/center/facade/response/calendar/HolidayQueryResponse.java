package com.payermax.channel.inst.center.facade.response.calendar;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2024/12/9 11:20
 */
@Getter
@Setter
@ToString
public class HolidayQueryResponse implements Serializable {


    private static final long serialVersionUID = 4753256071567480190L;
    /**
     * 开始日期(不为空)-包含
     */
    private String startDate;

    /**
     * 结束日期(不为空，且与startDate相差不超过365天)-包含
     */
    private String endDate;

    /**
     * 是否包含周六周日。
     * <p>
     * TRUE-结果中需要包含周六周日
     * FALSE-结果中不需要包含周六周日
     */
    private Boolean containsWeekends;

    /**
     * 区间段内包含的节假日
     */
    private List<HolidayItem> holidays;
}
