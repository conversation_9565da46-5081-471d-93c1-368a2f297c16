package com.payermax.channel.inst.center.facade.request.calendar;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2024/12/11 16:25
 */
@Getter
@Setter
@ToString
public class HolidayMultipleCheckRequest implements Serializable {
    private static final long serialVersionUID = -7237745749765316696L;

    /**
     * 需要多少个工作日(0<=N<365).
     * numOfWorkdays = 0 时判断当天是否为工作日，不为工作日则返回下一个工作日
     */
    private Integer numOfWorkdays;

    /**
     * 日期(包含)
     */
    private String date;

    /**
     * 多个国家OR币种的计算
     */
    private List<HolidayMultipleItem> multipleItems;

    /**
     * 日历类型，国家/币种/银行日历
     */
    private String calendarType;

}
