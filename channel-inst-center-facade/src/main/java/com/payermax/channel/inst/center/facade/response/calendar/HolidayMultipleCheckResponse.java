package com.payermax.channel.inst.center.facade.response.calendar;

import com.payermax.channel.inst.center.facade.request.calendar.HolidayMultipleItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2024/12/11 16:43
 */
@Getter
@Setter
@ToString
public class HolidayMultipleCheckResponse implements Serializable {
    private static final long serialVersionUID = 9012116360498346612L;

    //------REQUEST-----//
    /**
     * 需要多少个工作日(0<N<365).
     */
    private Integer numOfWorkdays;

    /**
     * 日期(包含)
     */
    private String date;

    /**
     * 多个国家OR币种的计算
     */
    private List<HolidayMultipleItem> multipleItems;

    /**
     * 日历类型，国家/币种/银行日历
     */
    private String calendarType;


    //------RESULT-----//
    /**
     * 目标日期.满足从开始日期(包含)开始,数够足够工作日的下一天。
     * <p>
     * 例如开始日期是周一，目标是3个工作日。
     * <p>
     * 假设周一到周三没有节假日的情况下，targetDate会最终返回周四的日期。
     * <p>
     * 假设周一为节假日，会从下一个工作日(周二)开始计算，targetDate 最终会返回周五的日期。
     */
    private String targetDate;

}
