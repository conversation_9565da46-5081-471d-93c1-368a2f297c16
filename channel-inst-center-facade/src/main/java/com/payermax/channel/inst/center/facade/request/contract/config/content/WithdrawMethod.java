package com.payermax.channel.inst.center.facade.request.contract.config.content;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> at 2023/6/12 8:26 PM
 **/
@AllArgsConstructor
@Getter
public enum WithdrawMethod {

    /**
     * 自动打款
     */
    MONEY_ARRIVE_AUTO("money-arrive-auto", "自动打款"),

    /**
     * 自主提现
     */
    WITHDRAW_MANUALLY("withdraw-manually", "自主提现");


    private final String code;

    private final String desc;

}
