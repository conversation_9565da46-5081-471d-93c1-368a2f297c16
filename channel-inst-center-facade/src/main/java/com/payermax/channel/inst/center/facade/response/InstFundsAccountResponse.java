package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新版资金账号详细信息
 */
@Data
public class InstFundsAccountResponse implements Serializable {

    private static final long serialVersionUID = 1313885732726674467L;

    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 机构开户名称
     */
    private String accountName;

    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;

    /**
     * 业务用途 I 代收/O 代付 / F 换汇
     */
    private String bizType;


    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 状态 N：不可用，Y：可用
     */
    private String status;

    /**
     * 关联机构MID
     */
    private String instMid;

    //----2023-04 新增的---//
    /**
     * 账户别名
     */
    private String accountAlias;

    /**
     * 账户类别(在岸OR离岸)
     */
    private String accountCategory;

    /**
     * 账户性质
     */
    private String accountNature;

    /**
     * 开户时间
     */
    private Date accountOpeningTime;

    /**
     * 授权签字人
     */
    private String authorizedOfficer;

    /**
     * 网银操作人
     */
    private String eBankOperator;

    /**
     * 相关文件
     */
    private String fileList;

    /**
     * iban
     */
    private String iban;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 财务属性, 财资户 | 客资户
     */
    private String financialAttribute;

    /**
     * 备注
     */
    private String memo;


    /**
     * 经办人信息
     * <p>
     * [
     * {
     * "role": "Operator",
     * "userId": "12222",
     * "name": "22222"
     * },
     * {
     * "role": "Operator",
     * "userId": "22222",
     * "name": "3333"
     * }
     * ]
     */
    private String bankOperator;


    /**
     * 是否支持网银
     */
    private String isSupportDirectLink;

    /**
     * 是否支持日内余额查询
     */
    private String isSupportIntradayBalance;

    /**
     * 是否支持日终余额查询
     */
    private String isSupportDayEndBalance;

    /**
     * 是否支持直联付款
     */
    private String isSupportDirectPayment;

    /**
     * 日终余额获取方式
     */
    private String dayEndBalanceMethod;

    /**
     * 日终余额获取时间
     */
    private String dayEndBalanceTime;

    /**
     * 日终余额时区
     */
    private String dayEndBalanceTimeZone;
}
