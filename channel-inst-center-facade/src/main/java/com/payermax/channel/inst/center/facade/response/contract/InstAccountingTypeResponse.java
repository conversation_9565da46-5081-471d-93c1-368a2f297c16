package com.payermax.channel.inst.center.facade.response.contract;

import com.payermax.channel.inst.center.facade.enums.contract.FeeTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstAccountingTypeResponse extends InstInfoQueryResponse {

    private static final long serialVersionUID = -8011776573080014655L;

    /**
     * 我司主体
     */
    private String entity;

    /**
     * 费用类型
     * {@link FeeTypeEnum}
     */
    private String feeType;

    /**
     * 渠道核对方式
     * {@link com.payermax.channel.inst.center.domain.enums.contract.management.AccountingTypeEnum}
     */
    private String accountingType;
}
