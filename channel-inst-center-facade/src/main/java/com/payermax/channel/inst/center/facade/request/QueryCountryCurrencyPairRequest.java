package com.payermax.channel.inst.center.facade.request;

import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 查找资金账号请求
 *
 * <AUTHOR>
 * @date 2022/10/2 15:01
 */
@Data
public class QueryCountryCurrencyPairRequest extends BaseRequest {

    private static final long serialVersionUID = -7199744093777143301L;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 国家
     */
    private List<String> country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 用途
     */
    @NotNull(message = "[useType] is mandatory")
    @Size(min = 1, message = "[useType] is mandatory")
    private List<UseTypeEnum> useTypes;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 是否支持子级账号
     */
    private String isSupportSubAccount;

    /**
     * 场景
     */
    private String scenes;

}
