package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

/**
 * QueryInstBrandRequest
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class QueryInstBrandRequest extends BaseRequest {

    private static final long serialVersionUID = 1318025734783356814L;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * bd shareId
     */
    private String bdId;

    /**
     * 状态，Y：可用、N：不可用，默认：Y
     */
    private String status = "Y";
}
