package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.enums.CycleUnit;
import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.service.ExchangeCalculator;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Objects;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 2:06 PM
 */
public class CyclicalSettleCalculator implements ExchangeCalculator {

    @Override
    public long calculate(SettleInfoVo settleInfoVo, long transactionTime) {
        AssertUtil.isTrue(!StringUtil.isEmpty(settleInfoVo.getSettleCycleUnit())
                , "ERROR", "[SettleCycleUnit] is mandatory ");
        CycleUnitHandler handler = CycleUnit.getByUnit(settleInfoVo.getSettleCycleUnit());
        AssertUtil.notNull(handler, "ERROR", "can not recognize SettleCycleUnit:" + settleInfoVo.getSettleCycleUnit());
        long time = Objects.requireNonNull(handler).calculateCycleTime(settleInfoVo, transactionTime);
        AssertUtil.isTrue(time >= transactionTime, "ERROR", "结算时间不能在过去");
        return time;
    }
}
