package com.payermax.channel.inst.center.facade.request.contract;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @DESC 机构合约批量校验
 */
@Data
public class InstContractBatchVerifyRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotEmpty
    private List<VerifyItem> verifyList;


    @Data
    public static class VerifyItem implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 机构编码
         * <p>比如 WORLDPAYHK01</p>
         */
        private String instCode;

        /**
         * 签约主体
         * <p>比如 P01</p>
         */
        private String entity;

        /**
         * 业务类型
         * <p>比如 I、O</p>
         * {@link com.payermax.channel.inst.center.facade.enums.contract.ContractBizTypeEnum}
         */
        private String bizType;

        /**
         * 支付方式类型
         * <p>比如 CARDPAY</p>
         */
        private String paymentMethodType;

        /**
         * 目标机构
         * <p>比如 DANA</p>
         */
        private String targetOrg;

        /**
         * 目标卡组
         * <p>比如 CARDPAY</p>
         */
        private String cardOrg;

        /**
         * 币种列表
         */
        private List<String> ccyList;

        /**
         * 渠道商户号列表
         */
        private List<String> midList;

    }
}
