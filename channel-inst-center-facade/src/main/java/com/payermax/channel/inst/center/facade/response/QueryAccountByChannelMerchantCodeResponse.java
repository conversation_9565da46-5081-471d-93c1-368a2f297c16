package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> at 2025/7/3 10:53
 **/
@Data
public class QueryAccountByChannelMerchantCodeResponse implements Serializable {

    /**
     * key: channelMerchantCode            金融交换包装的渠道商户号
     * value: InstFundsAccountResponse     关联的出款资金账户
     */
    private Map<String, List<InstFundsAccountResponse>> channelMerchantCodeAccountsMap;

}
