package com.payermax.channel.inst.center.facade.response.contract;

import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/11/4
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class InstRiskFeeInfoResponse extends InstInfoQueryResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 风险产品大类 - cbAlert
     */
    private String riskProductType;

    /**
     * 风险产品具体类型
     */
    private String riskProductSpecificType;

    /**
     * 预警币种
     */
    private String riskTradeCurrency;
}
