package com.payermax.channel.inst.center.facade.request.contract.config.sub;

import com.payermax.channel.inst.center.facade.request.contract.config.content.WithdrawMethod;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> at 2023/6/12 7:58 PM
 **/
@Setter
@Getter
public class SettleAccount implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 最小提现金额
     */
    private BigDecimal minSettleAmount;

    /**
     * 提现方式
     * <p>
     * {@link  WithdrawMethod}
     */
    private String withdrawMethod;

    private String accountId;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;
}
