package com.payermax.channel.inst.center.facade.request.contract;

import com.payermax.channel.inst.center.facade.enums.contract.FeeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstAccountingTypeQueryRequest extends InstInfoQueryRequest{

    private static final long serialVersionUID = 1L;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 我司主体
     */
    private String entity;

    /**
     * 费用类型
     * {@link FeeTypeEnum}
     */
    private String feeType;

}
