package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> at 2022/10/15 23:11
 **/
@Data
public class PatchSubAccountCompensateRequest extends BaseRequest {

    private static final long serialVersionUID = -7550337869826L;
    
    /**
     * 机构账号
     */
    private List<String> accountIds;

    /**
     * 子级资金账号
     */
    private List<String> subAccountIds;

    /**
     * 商户号
     */
    private List<String> merchantNos;

    /**
     * 补偿子级账号状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    @NotNull(message = "status can not null")
    @Min(0)
    private Integer status;

    /**
     * 最近多少小时的数据
     */
    @NotNull(message = "recentHour can not null")
    @Min(1)
    private Integer recentHour;

    /**
     * 分页大小
     */
    @NotNull(message = "pageSize can not null")
    @Min(1)
    private int pageSize;


}
