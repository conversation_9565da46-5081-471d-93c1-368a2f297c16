package com.payermax.channel.inst.center.facade.response.contract;

import com.payermax.channel.inst.center.facade.enums.contract.ContractBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/20
 * @DESC
 */
@Data
public class InstContractMidQueryResponse implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 合约编号
     */
    private String contractNo;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 渠道商户号
     */
    private String channelMerchantCode;

    /**
     * 合约类型
     * {@link ContractBizTypeEnum}
     */
    private String instType;
}
