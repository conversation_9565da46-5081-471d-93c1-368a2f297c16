<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-inst-center-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <artifactId>channel-inst-center-facade</artifactId>
    <packaging>jar</packaging>
    <name>channel-inst-center-facade</name>
    <version>${facade-revision}</version>
    <dependencies>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>common-dto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>operating-omc-workflow-facade</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>测试项目</projectName>
                    <excludes>
                        <!--1.0.7版本开始你还可以用正则匹配排除,如：poi.* -->
                        <exclude>com.alibaba:fastjson</exclude>
                    </excludes>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <include>com.alibaba:fastjson</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <!-- 两个ID必须与 setting.xml中的<server><id>Releases</id></server>保持一致 -->
        <repository>
            <id>Releases</id>
            <name>Nexus Release Repository</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>Snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
